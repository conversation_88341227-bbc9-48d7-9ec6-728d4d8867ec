'use client';

import React from 'react';

interface WeatherData {
  location: string;
  currentTime: string;
  temperature: number;
  feelsLike: number;
  condition: string;
  description: string;
  humidity: number;
  precipitation: number;
  radiation: number;
  windDirection: string;
  windSpeed: number;
  pressure: number;
  co2: number;
  airQuality: {
    index: number;
    pm1: number;
    pm25: number;
    pm10: number;
    co: number;
    no: number;
    no2: number;
    o2: number;
  };
}

// Mock weather data - in a real app this would come from an API
const weatherData: WeatherData = {
  location: "Lincoln, England, United Kingdom",
  currentTime: "9:07 AM",
  temperature: 15,
  feelsLike: 17,
  condition: "mostly_sunny",
  description: "Expect partly sunny skies. The high will be 19°.",
  humidity: 64,
  precipitation: 0,
  radiation: 245,
  windDirection: "NW",
  windSpeed: 13,
  pressure: 1012,
  co2: 415,
  airQuality: {
    index: 26,
    pm1: 8.2,
    pm25: 12.5,
    pm10: 18.3,
    co: 0.4,
    no: 2.1,
    no2: 15.7,
    o2: 20.9
  }
};

const WeatherIcon: React.FC<{ condition: string; size?: number }> = ({ condition, size = 64 }) => {
  const getIcon = () => {
    switch (condition) {
      case 'sunny':
        return (
          <div className="relative">
            <div className="w-16 h-16 bg-yellow-400 rounded-full shadow-lg" />
            <div className="absolute inset-0 animate-pulse">
              <div className="w-16 h-16 bg-yellow-300 rounded-full opacity-60" />
            </div>
          </div>
        );
      case 'mostly_sunny':
        return (
          <div className="relative flex items-center">
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full shadow-lg" />
            <div className="w-8 h-8 bg-white rounded-full ml-2 shadow-lg border-2 border-gray-200 opacity-80" />
          </div>
        );
      case 'cloudy':
        return <div className="w-16 h-12 bg-gray-300 rounded-full relative shadow-lg" />;
      case 'rainy':
        return (
          <div className="relative">
            <div className="w-16 h-12 bg-gray-500 rounded-full shadow-lg" />
            <div className="absolute bottom-0 left-2 w-1 h-3 bg-blue-400 rounded-full" />
            <div className="absolute bottom-0 left-4 w-1 h-4 bg-blue-400 rounded-full" />
            <div className="absolute bottom-0 left-6 w-1 h-3 bg-blue-400 rounded-full" />
          </div>
        );
      default:
        return <div className="w-16 h-12 bg-blue-400 rounded-full shadow-lg" />;
    }
  };

  return <div className="flex items-center justify-center">{getIcon()}</div>;
};

const getBackgroundGradient = (condition: string, temperature: number) => {
  const timeOfDay = new Date().getHours();
  const isNight = timeOfDay < 6 || timeOfDay > 20;
  
  if (isNight) {
    return 'bg-gradient-to-br from-indigo-900 via-purple-900 to-blue-900';
  }
  
  switch (condition) {
    case 'sunny':
      return temperature > 25 
        ? 'bg-gradient-to-br from-orange-400 via-yellow-400 to-amber-300'
        : 'bg-gradient-to-br from-blue-400 via-sky-400 to-cyan-300';
    case 'mostly_sunny':
      return 'bg-gradient-to-br from-blue-500 via-sky-400 to-blue-300';
    case 'cloudy':
      return 'bg-gradient-to-br from-gray-500 via-gray-400 to-slate-400';
    case 'rainy':
      return 'bg-gradient-to-br from-gray-600 via-slate-500 to-gray-500';
    default:
      return 'bg-gradient-to-br from-blue-500 via-sky-400 to-blue-300';
  }
};

export const AdvancedWeatherCard: React.FC = () => {
  const backgroundClass = getBackgroundGradient(weatherData.condition, weatherData.temperature);

  return (
    <div className={`${backgroundClass} rounded-2xl shadow-lg p-6 text-white h-full relative overflow-hidden`}>
      {/* Background overlay for better text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-20 rounded-2xl" />

      <div className="relative z-10 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <div className="flex items-center gap-2 mb-1">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z" stroke="currentColor" strokeWidth="2" fill="none"/>
                <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2" fill="none"/>
              </svg>
              <span className="text-sm font-medium opacity-90">{weatherData.location}</span>
            </div>
            <div className="text-xs opacity-75">Current weather</div>
            <div className="text-xs opacity-75">{weatherData.currentTime}</div>
          </div>
          <button className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg px-3 py-1 text-xs transition-all backdrop-blur-sm">
            💬 Seeing different weather?
          </button>
        </div>

        {/* Main weather display */}
        <div className="flex items-center gap-6 mb-6">
          <WeatherIcon condition={weatherData.condition} />
          <div>
            <div className="text-5xl font-bold mb-1">{weatherData.temperature}°C</div>
            <div className="text-xl opacity-90 capitalize mb-1">{weatherData.condition.replace('_', ' ')}</div>
            <div className="text-sm opacity-75">Feels like {weatherData.feelsLike}°</div>
          </div>
        </div>

        {/* Description */}
        <div className="text-sm opacity-90 mb-6 bg-white bg-opacity-10 rounded-lg p-3 backdrop-blur-sm">
          {weatherData.description}
        </div>

        {/* Weather metrics grid */}
        <div className="grid grid-cols-6 gap-3 text-xs mb-4">
          {/* Row 1 */}
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">Air quality</div>
            <div className="font-semibold text-sm">{weatherData.airQuality.index}</div>
          </div>
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">Wind</div>
            <div className="font-semibold text-sm">{weatherData.windSpeed} km/h ↗</div>
            <div className="opacity-75 text-xs">{weatherData.windDirection}</div>
          </div>
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">Humidity</div>
            <div className="font-semibold text-sm">{weatherData.humidity}%</div>
          </div>
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">Visibility</div>
            <div className="font-semibold text-sm">48.3 km</div>
          </div>
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">Pressure</div>
            <div className="font-semibold text-sm">{weatherData.pressure} mb</div>
          </div>
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">Dew point</div>
            <div className="font-semibold text-sm">9°</div>
          </div>
        </div>

        {/* Additional metrics */}
        <div className="grid grid-cols-4 gap-3 text-xs mb-4">
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">Radiation</div>
            <div className="font-semibold text-sm">{weatherData.radiation} W/m²</div>
          </div>
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">CO₂</div>
            <div className="font-semibold text-sm">{weatherData.co2} ppm</div>
          </div>
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">PM2.5</div>
            <div className="font-semibold text-sm">{weatherData.airQuality.pm25} μg/m³</div>
          </div>
          <div className="text-center bg-white bg-opacity-10 rounded-lg p-2 backdrop-blur-sm">
            <div className="opacity-75 mb-1">O₂</div>
            <div className="font-semibold text-sm">{weatherData.airQuality.o2}%</div>
          </div>
        </div>

        {/* Air quality details */}
        <div className="bg-white bg-opacity-10 rounded-lg p-3 backdrop-blur-sm">
          <div className="text-xs opacity-75 mb-3 font-medium">Air Quality Details</div>
          <div className="grid grid-cols-6 gap-3 text-xs">
            <div className="text-center">
              <div className="opacity-75 mb-1">PM1</div>
              <div className="font-semibold">{weatherData.airQuality.pm1}</div>
            </div>
            <div className="text-center">
              <div className="opacity-75 mb-1">PM10</div>
              <div className="font-semibold">{weatherData.airQuality.pm10}</div>
            </div>
            <div className="text-center">
              <div className="opacity-75 mb-1">CO</div>
              <div className="font-semibold">{weatherData.airQuality.co}</div>
            </div>
            <div className="text-center">
              <div className="opacity-75 mb-1">NO</div>
              <div className="font-semibold">{weatherData.airQuality.no}</div>
            </div>
            <div className="text-center">
              <div className="opacity-75 mb-1">NO₂</div>
              <div className="font-semibold">{weatherData.airQuality.no2}</div>
            </div>
            <div className="text-center">
              <div className="opacity-75 mb-1">Precip.</div>
              <div className="font-semibold">{weatherData.precipitation}mm</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

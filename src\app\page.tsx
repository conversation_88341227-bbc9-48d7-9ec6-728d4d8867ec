'use client';

import React, { useState } from 'react';
import { DashboardSettings } from '../components/DashboardSettings';
import { FieldCard } from '../components/cards/FieldCard';
import { WeatherCard } from '../components/cards/WeatherCard';
import { WeatherMetricsCard } from '../components/cards/WeatherMetricsCard';
import { WeatherForecastCard } from '../components/cards/WeatherForecastCard';
import { FieldStatusCard } from '../components/cards/FieldStatusCard';
import { useDashboardLayout } from '../hooks/useDashboardLayout';

const mapPlaceholder = "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=200&q=80";

export default function Home() {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const { getVisibleCards, isLoading } = useDashboardLayout();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-200 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }
  const visibleCards = getVisibleCards();

  const renderCard = (cardId: string) => {
    switch (cardId) {
      case 'wheat-field':
        return (
          <div className="col-span-1">
            <div className="h-48">
              <FieldCard
                title="Wheat field"
                area="22 ha"
                imageUrl="https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?auto=format&fit=crop&w=400&q=80"
                alt="Wheat field"
              />
            </div>
          </div>
        );
      case 'weather':
        return (
          <div className="col-span-6">
            <div className="h-80">
              <WeatherCard />
            </div>
          </div>
        );
      case 'left-field':
        return (
          <div className="col-span-1">
            <div className="h-48">
              <FieldCard
                title="Corn field"
                area="18 ha"
                imageUrl="https://images.unsplash.com/photo-1501785888041-af3ef285b470?auto=format&fit=crop&w=400&q=80"
                alt="Corn field"
              />
            </div>
          </div>
        );
      case 'soybean-field':
        return (
          <div className="col-span-1">
            <div className="h-48">
              <FieldCard
                title="Soybean field"
                area="16 ha"
                imageUrl="https://images.unsplash.com/photo-1625246333195-78d9c38ad449?auto=format&fit=crop&w=400&q=80"
                alt="Soybean field"
              />
            </div>
          </div>
        );
      case 'right-field':
        return (
          <div className="col-span-1">
            <div className="h-48">
              <FieldCard
                title="Empty field"
                area="15 ha"
                imageUrl={mapPlaceholder}
                alt="Empty field"
              />
            </div>
          </div>
        );




      case 'weather-metrics':
        return (
          <div className="col-span-6">
            <div className="h-32">
              <WeatherMetricsCard />
            </div>
          </div>
        );

      case 'field-status':
        return (
          <div className="col-span-4">
            <div className="h-80">
              <FieldStatusCard />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <main className="min-h-screen bg-gray-200 relative overflow-hidden font-sans">
      {/* Background */}
      <div className="absolute inset-0 -z-10 bg-gradient-to-br from-gray-100 to-gray-300" />

      {/* Settings Button */}
      <button
        onClick={() => setIsSettingsOpen(true)}
        className="fixed top-4 right-4 z-40 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-colors"
        title="Dashboard Settings"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2"/>
          <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2579 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.01127 9.77251C4.28054 9.5799 4.48571 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" strokeWidth="2"/>
        </svg>
      </button>



      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Field Cards Row */}
        <div className="grid grid-cols-6 gap-6">
          {visibleCards.filter(cardId => ['wheat-field', 'left-field', 'soybean-field', 'right-field'].includes(cardId)).map(cardId => {
            // Render field cards without drag functionality for now
            const cardContent = renderCard(cardId);
            return cardContent ? React.cloneElement(cardContent, { key: cardId }) : null;
          })}
        </div>

        {/* Weather Chart Row */}
        {visibleCards.includes('weather') && (
          <div className="grid grid-cols-6 gap-6">
            {renderCard('weather')}
          </div>
        )}

        {/* Weather Metrics Row */}
        {visibleCards.includes('weather-metrics') && (
          <div className="grid grid-cols-6 gap-6">
            {renderCard('weather-metrics')}
          </div>
        )}

        {/* Weather Forecast and Field Status Row with Recommendations */}
        {(visibleCards.includes('weather-forecast') || visibleCards.includes('field-status')) && (
          <div className="grid grid-cols-6 gap-6">
            <div className="col-span-4 space-y-6">
              {/* Weather Forecast */}
              {visibleCards.includes('weather-forecast') && (
                <div className="h-96">
                  <WeatherForecastCard />
                </div>
              )}

              {/* Field Status */}
              {visibleCards.includes('field-status') && (
                <div>
                  {renderCard('field-status')}
                </div>
              )}
            </div>

            {/* Right side - Recommendations spanning full height */}
            {visibleCards.includes('recommendations') && (
              <div className="col-span-2">
                <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100 h-full min-h-[42rem]">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#3B82F6"/>
                      </svg>
                    </div>
                    <span className="text-base font-semibold text-gray-800">Recommendations</span>
                  </div>

                  <div className="flex flex-col gap-2 flex-1 overflow-y-auto pr-1">
                    <div className="bg-blue-50 rounded-lg p-2 border-l-3 border-blue-400">
                      <div className="flex items-center gap-2 mb-1">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" className="flex-shrink-0">
                          <path d="M12 3C12 3 20 13 20 19C20 22 16.4183 25 12 25C7.5817 25 4 22 4 19C4 13 12 3 12 3Z" fill="#3B82F6"/>
                        </svg>
                        <span className="text-sm font-semibold text-blue-800">Irrigation</span>
                      </div>
                      <p className="text-sm text-blue-700 leading-relaxed">Increase watering by 15% due to low humidity (74%)</p>
                    </div>

                    <div className="bg-green-50 rounded-lg p-2 border-l-3 border-green-400">
                      <div className="flex items-center gap-2 mb-1">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" className="flex-shrink-0">
                          <path d="M12 2L12 22M7 7L12 2L17 7M7 17L12 22L17 17" stroke="#22C55E" strokeWidth="2" fill="none"/>
                        </svg>
                        <span className="text-sm font-semibold text-green-800">Fertilizer</span>
                      </div>
                      <p className="text-sm text-green-700 leading-relaxed">Apply phosphorus supplement (current: 10.5)</p>
                    </div>

                    <div className="bg-orange-50 rounded-lg p-2 border-l-3 border-orange-400">
                      <div className="flex items-center gap-2 mb-1">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" className="flex-shrink-0">
                          <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#F97316" strokeWidth="2" fill="none"/>
                        </svg>
                        <span className="text-sm font-semibold text-orange-800">Weather</span>
                      </div>
                      <p className="text-sm text-orange-700 leading-relaxed">Rain expected in 2 days. Plan harvest accordingly</p>
                    </div>

                    <div className="bg-red-50 rounded-lg p-2 border-l-3 border-red-400">
                      <div className="flex items-center gap-2 mb-1">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" className="flex-shrink-0">
                          <circle cx="12" cy="12" r="3" fill="#EF4444"/>
                          <path d="M12 1V5M12 19V23M4.22 4.22L6.34 6.34M17.66 17.66L19.78 19.78M1 12H5M19 12H23M4.22 19.78L6.34 17.66M17.66 6.34L19.78 4.22" stroke="#EF4444" strokeWidth="2"/>
                        </svg>
                        <span className="text-sm font-semibold text-red-800">Pest Alert</span>
                      </div>
                      <p className="text-sm text-red-700 leading-relaxed">Monitor for corn borer activity in warm weather</p>
                    </div>
                  </div>

                  <button className="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors mt-3 flex-shrink-0">
                    View All
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Settings Modal */}
      <DashboardSettings
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
      />
    </main>
  );
}

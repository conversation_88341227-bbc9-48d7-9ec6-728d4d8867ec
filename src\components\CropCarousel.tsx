'use client';

import React, { useRef, useState } from 'react';
import Image from 'next/image';
import { Crop } from '../types/dashboard';

interface CropCarouselProps {
  crops: Crop[];
  selectedCropId: string | null;
  onSelectCrop: (cropId: string) => void;
  onAddCrop: () => void;
}

const CropCard: React.FC<{
  crop: Crop;
  isSelected: boolean;
  onClick: () => void;
}> = ({ crop, isSelected, onClick }) => {
  const getStatusColor = (status: Crop['status']) => {
    switch (status) {
      case 'planted': return 'bg-blue-100 text-blue-800';
      case 'growing': return 'bg-green-100 text-green-800';
      case 'ready': return 'bg-yellow-100 text-yellow-800';
      case 'harvested': return 'bg-gray-100 text-gray-800';
      case 'empty': return 'bg-gray-100 text-gray-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getStatusText = (status: Crop['status']) => {
    switch (status) {
      case 'planted': return 'Planted';
      case 'growing': return 'Growing';
      case 'ready': return 'Ready';
      case 'harvested': return 'Harvested';
      case 'empty': return 'Empty';
      default: return 'Unknown';
    }
  };

  return (
    <div
      onClick={onClick}
      className={`
        flex-shrink-0 w-64 h-48 bg-white rounded-2xl shadow-lg border-2 cursor-pointer transition-all duration-200 hover:shadow-xl hover:scale-105
        ${isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-100 hover:border-gray-200'}
      `}
    >
      <div className="p-4 h-full flex flex-col">
        {/* Image Section */}
        <div className="relative w-full h-24 mb-3 rounded-xl overflow-hidden">
          <Image 
            src={crop.imageUrl} 
            alt={crop.name} 
            fill 
            className="object-cover"
          />
          {/* Overlay polygon for field visualization */}
          <svg className="absolute inset-0 w-full h-full" viewBox="0 0 112 80" fill="none">
            <polygon 
              points="22,58 38,28 85,22 95,48 68,68" 
              fill="#D9F99D" 
              fillOpacity="0.5" 
              stroke="#A3E635" 
              strokeWidth="2" 
            />
          </svg>
          {/* Status badge */}
          <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(crop.status)}`}>
            {getStatusText(crop.status)}
          </div>
        </div>

        {/* Content Section */}
        <div className="flex-1 flex flex-col justify-between">
          <div>
            <h3 className="font-bold text-lg text-gray-800 truncate mb-1">
              {crop.name}
            </h3>
            <p className="text-sm text-gray-600 capitalize mb-2">
              {crop.type === 'other' ? 'Mixed/Other' : crop.type}
            </p>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              {crop.area} ha
            </div>
            {isSelected && (
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const AddCropCard: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <div
      onClick={onClick}
      className="flex-shrink-0 w-64 h-48 bg-white rounded-2xl shadow-lg border-2 border-dashed border-gray-300 cursor-pointer transition-all duration-200 hover:shadow-xl hover:border-blue-400 hover:bg-blue-50"
    >
      <div className="h-full flex flex-col items-center justify-center text-gray-500 hover:text-blue-600 transition-colors">
        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3 group-hover:bg-blue-100">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        <span className="font-medium text-sm">Add New Crop</span>
        <span className="text-xs text-gray-400 mt-1">Click to register</span>
      </div>
    </div>
  );
};

export const CropCarousel: React.FC<CropCarouselProps> = ({
  crops,
  selectedCropId,
  onSelectCrop,
  onAddCrop,
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -280, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 280, behavior: 'smooth' });
    }
  };

  return (
    <div className="relative">
      {/* Left Arrow */}
      {showLeftArrow && (
        <button
          onClick={scrollLeft}
          className="absolute left-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white rounded-full shadow-lg border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      )}

      {/* Right Arrow */}
      {showRightArrow && (
        <button
          onClick={scrollRight}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white rounded-full shadow-lg border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      )}

      {/* Scrollable Container */}
      <div
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className="flex gap-4 overflow-x-auto scrollbar-hide pb-2"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {crops.map((crop) => (
          <CropCard
            key={crop.id}
            crop={crop}
            isSelected={selectedCropId === crop.id}
            onClick={() => onSelectCrop(crop.id)}
          />
        ))}
        
        {/* Add Crop Card */}
        <AddCropCard onClick={onAddCrop} />
      </div>
    </div>
  );
};
